import React, { useEffect, useRef } from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
    LineElement,
    PointElement
} from 'chart.js';

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
    LineElement,
    PointElement
);

const ESGChart = ({ 
    id, 
    type = 'bar', 
    data, 
    title, 
    height = 300,
    options = {},
    onChartReady = null 
}) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        if (chartRef.current && data) {
            // Destroy existing chart if it exists
            if (chartInstance.current) {
                chartInstance.current.destroy();
            }

            const ctx = chartRef.current.getContext('2d');
            
            // Default chart options
            const defaultOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: !!title,
                        text: title,
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#315975'
                    },
                    legend: {
                        display: type === 'pie' || type === 'doughnut' || (data.datasets && data.datasets.length > 1),
                        position: 'top',
                        labels: {
                            color: '#315975',
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#149CE0',
                        borderWidth: 1
                    }
                },
                scales: type === 'pie' || type === 'doughnut' ? {} : {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#315975',
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(49, 89, 117, 0.1)'
                        },
                        ticks: {
                            color: '#315975',
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            };

            // Merge with custom options
            const mergedOptions = {
                ...defaultOptions,
                ...options,
                plugins: {
                    ...defaultOptions.plugins,
                    ...options.plugins
                }
            };

            // Create new chart
            chartInstance.current = new ChartJS(ctx, {
                type: type,
                data: data,
                options: mergedOptions
            });

            if (onChartReady) {
                onChartReady(chartInstance.current);
            }
        }

        // Cleanup function
        return () => {
            if (chartInstance.current) {
                chartInstance.current.destroy();
                chartInstance.current = null;
            }
        };
    }, [data, type, title, options]);

    return (
        <div style={{ height: `${height}px`, position: 'relative' }}>
            <canvas 
                ref={chartRef} 
                id={id}
                style={{ maxHeight: '100%', maxWidth: '100%' }}
            />
        </div>
    );
};

// Predefined chart configurations for ESG metrics
export const ESGChartConfigs = {
    // Highlights Section Charts
    carbonIntensity: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Carbon Intensity (tCO₂e/Revenue)'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'tCO₂e per Million SGD'
                    }
                }
            }
        }
    },

    carbonFootprint: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Scope 1+2 Market Based (Carbon Footprint)'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'tCO₂e'
                    }
                }
            }
        }
    },

    renewableEnergyFactor: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Renewable Energy Factor (REF)'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'Percentage (%)'
                    },
                    max: 100
                }
            }
        }
    },

    powerUsageEffectiveness: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Power Usage Effectiveness (PUE)'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'PUE Ratio'
                    }
                }
            }
        }
    },

    waterUsageEffectiveness: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Water Usage Effectiveness (WUE)'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'WUE Ratio'
                    }
                }
            }
        }
    },

    // Environment Section Charts
    scope1And2Emissions: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Scope 1+2 GHG Emissions (Target vs Actual)'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'tCO₂e'
                    }
                }
            }
        }
    },

    electricityConsumption: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Electricity Consumption - Renewable vs Non-renewable'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'kWh'
                    }
                }
            }
        }
    },

    renewablesBreakdown: {
        type: 'pie',
        options: {
            plugins: {
                title: {
                    text: 'Renewables Breakdown by Type'
                }
            }
        }
    },

    waterWithdrawn: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Water Withdrawn'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'Cubic Meters (m³)'
                    }
                }
            }
        }
    },

    wasteGenerated: {
        type: 'bar',
        options: {
            plugins: {
                title: {
                    text: 'Waste Generated'
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'Tonnes'
                    }
                }
            }
        }
    }
};

// Color schemes for different chart types
export const ESGColorSchemes = {
    primary: ['#149CE0', '#94D0F8', '#B9FCFC'],
    environment: ['#4CAF50', '#81C784', '#A5D6A7'],
    social: ['#FF9800', '#FFB74D', '#FFCC02'],
    governance: ['#9C27B0', '#BA68C8', '#CE93D8'],
    comparison: ['#149CE0', '#FF5722'],
    renewable: ['#4CAF50', '#FF5722', '#FFC107', '#2196F3']
};

export default ESGChart;
